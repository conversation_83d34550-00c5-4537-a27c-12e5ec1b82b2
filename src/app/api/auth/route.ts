import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  
  if (code) {
    // Handle OAuth callback
    try {
      const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: process.env.GITHUB_CLIENT_ID,
          client_secret: process.env.GITHUB_CLIENT_SECRET,
          code: code,
        }),
      });
      
      const tokenData = await tokenResponse.json();
      
      if (tokenData.access_token) {
        const postMsgContent = {
          token: tokenData.access_token,
          provider: "github"
        };

        return new NextResponse(`
          <script>
            (function() {
              function receiveMessage(e) {
                console.log("receiveMessage %o", e);

                // send message to main window with the app
                window.opener.postMessage(
                  'authorization:github:success:${JSON.stringify(postMsgContent)}',
                  e.origin
                );
              }

              window.addEventListener("message", receiveMessage, false);
              window.opener.postMessage("authorizing:github", "*");
            })()
          </script>
        `, {
          headers: { 'Content-Type': 'text/html' }
        });
      } else {
        return NextResponse.json({ error: 'Failed to get access token' }, { status: 400 });
      }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_) {
      return NextResponse.json({ error: 'OAuth error' }, { status: 500 });
    }
  } else {
    // Handle auth initiation
    const clientId = process.env.GITHUB_CLIENT_ID;
    const authURL = `https://github.com/login/oauth/authorize?client_id=${clientId}&scope=repo&state=${state || 'decap'}`;
    
    return NextResponse.redirect(authURL);
  }
}